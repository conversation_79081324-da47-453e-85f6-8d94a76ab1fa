/* DramaCraft 企业级文档自定义样式 */

/* CSS变量定义 */
:root {
  /* 品牌色彩 */
  --dc-primary: #1e3a8a;
  --dc-primary-light: #3b82f6;
  --dc-primary-dark: #1e40af;
  --dc-secondary: #f59e0b;
  --dc-secondary-light: #fbbf24;
  --dc-secondary-dark: #d97706;
  
  /* 渐变色 */
  --dc-gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  --dc-gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  --dc-gradient-hero: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #f59e0b 100%);
  
  /* 阴影 */
  --dc-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --dc-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --dc-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --dc-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 圆角 */
  --dc-radius-sm: 0.375rem;
  --dc-radius-md: 0.5rem;
  --dc-radius-lg: 0.75rem;
  --dc-radius-xl: 1rem;
  
  /* 间距 */
  --dc-space-xs: 0.5rem;
  --dc-space-sm: 0.75rem;
  --dc-space-md: 1rem;
  --dc-space-lg: 1.5rem;
  --dc-space-xl: 2rem;
  --dc-space-2xl: 3rem;
}

/* 深色模式变量 */
[data-md-color-scheme="slate"] {
  --dc-bg-hero: rgba(30, 58, 138, 0.1);
  --dc-text-muted: rgba(255, 255, 255, 0.7);
}

/* 浅色模式变量 */
[data-md-color-scheme="default"] {
  --dc-bg-hero: rgba(30, 58, 138, 0.05);
  --dc-text-muted: rgba(0, 0, 0, 0.6);
}

/* 首页英雄区域 */
.hero-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--dc-space-2xl);
  align-items: center;
  padding: var(--dc-space-2xl) 0;
  margin-bottom: var(--dc-space-2xl);
  background: var(--dc-bg-hero);
  border-radius: var(--dc-radius-xl);
  padding: var(--dc-space-2xl);
}

.hero-content {
  max-width: 100%;
}

.hero-title {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--dc-space-lg);
}

.gradient-text {
  background: var(--dc-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.highlight-text {
  color: var(--dc-secondary);
}

.hero-description {
  font-size: 1.25rem;
  color: var(--dc-text-muted);
  margin-bottom: var(--dc-space-xl);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--dc-space-md);
  flex-wrap: wrap;
}

.hero-image {
  text-align: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: var(--dc-radius-lg);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--dc-space-xs);
  padding: var(--dc-space-sm) var(--dc-space-lg);
  border-radius: var(--dc-radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.btn-primary {
  background: var(--dc-gradient-primary);
  color: white;
  box-shadow: var(--dc-shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--dc-shadow-lg);
  color: white;
}

.btn-secondary {
  background: var(--dc-gradient-secondary);
  color: white;
  box-shadow: var(--dc-shadow-md);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--dc-shadow-lg);
  color: white;
}

.btn-outline {
  border-color: var(--dc-primary);
  color: var(--dc-primary);
}

.btn-outline:hover {
  background: var(--dc-primary);
  color: white;
}

.btn-large {
  padding: var(--dc-space-md) var(--dc-space-xl);
  font-size: 1.125rem;
}

/* 特性网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--dc-space-lg);
  margin: var(--dc-space-2xl) 0;
}

.feature-card {
  padding: var(--dc-space-xl);
  border-radius: var(--dc-radius-lg);
  box-shadow: var(--dc-shadow-md);
  transition: all 0.3s ease;
  background: var(--md-default-bg-color);
  border: 1px solid var(--md-default-fg-color--lightest);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--dc-shadow-xl);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: var(--dc-space-md);
  color: var(--dc-primary);
}

.feature-card h3 {
  margin-bottom: var(--dc-space-sm);
  color: var(--md-default-fg-color);
}

.feature-card p {
  color: var(--dc-text-muted);
  line-height: 1.6;
}

/* 性能指标 */
.metrics-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--dc-space-lg);
  margin: var(--dc-space-2xl) 0;
  padding: var(--dc-space-xl);
  background: var(--dc-bg-hero);
  border-radius: var(--dc-radius-lg);
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--dc-primary);
  margin-bottom: var(--dc-space-xs);
}

.metric-label {
  font-size: 0.875rem;
  color: var(--dc-text-muted);
  font-weight: 500;
}

/* 演示区域 */
.demo-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--dc-space-lg);
  margin: var(--dc-space-2xl) 0;
}

.demo-item {
  padding: var(--dc-space-lg);
  border-radius: var(--dc-radius-lg);
  border: 2px solid var(--md-default-fg-color--lightest);
  transition: all 0.3s ease;
}

.demo-item:hover {
  border-color: var(--dc-primary);
  transform: translateY(-2px);
}

.demo-item h3 {
  margin-bottom: var(--dc-space-sm);
  color: var(--dc-primary);
}

.demo-link {
  color: var(--dc-secondary);
  text-decoration: none;
  font-weight: 600;
}

.demo-link:hover {
  color: var(--dc-secondary-dark);
}

/* 使用场景 */
.use-cases {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--dc-space-lg);
  margin: var(--dc-space-2xl) 0;
}

.use-case {
  padding: var(--dc-space-lg);
  border-radius: var(--dc-radius-lg);
  background: var(--dc-bg-hero);
}

.use-case h3 {
  margin-bottom: var(--dc-space-md);
  color: var(--dc-primary);
}

.use-case ul {
  list-style: none;
  padding: 0;
}

.use-case li {
  padding: var(--dc-space-xs) 0;
  position: relative;
  padding-left: var(--dc-space-lg);
}

.use-case li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--dc-secondary);
  font-weight: bold;
}

/* 技术优势 */
.advantages {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--dc-space-lg);
  margin: var(--dc-space-2xl) 0;
}

.advantage-item {
  text-align: center;
  padding: var(--dc-space-lg);
}

.advantage-item h4 {
  margin-bottom: var(--dc-space-sm);
  color: var(--dc-primary);
}

/* CTA区域 */
.cta-section {
  text-align: center;
  padding: var(--dc-space-2xl);
  background: var(--dc-gradient-hero);
  border-radius: var(--dc-radius-xl);
  margin: var(--dc-space-2xl) 0;
  color: white;
}

.cta-section h2 {
  color: white;
  margin-bottom: var(--dc-space-md);
}

.cta-section p {
  font-size: 1.125rem;
  margin-bottom: var(--dc-space-xl);
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: var(--dc-space-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* 社区区域 */
.community-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--dc-space-lg);
  margin: var(--dc-space-2xl) 0;
}

.community-item {
  padding: var(--dc-space-lg);
  border-radius: var(--dc-radius-lg);
  border: 1px solid var(--md-default-fg-color--lightest);
  text-align: center;
}

.community-item h3 {
  margin-bottom: var(--dc-space-sm);
  color: var(--dc-primary);
}

.community-link {
  color: var(--dc-secondary);
  text-decoration: none;
  font-weight: 600;
}

.community-link:hover {
  color: var(--dc-secondary-dark);
}

/* 页脚注释 */
.footer-note {
  text-align: center;
  padding: var(--dc-space-xl) 0;
  border-top: 1px solid var(--md-default-fg-color--lightest);
  margin-top: var(--dc-space-2xl);
}

.footer-note p {
  margin-bottom: var(--dc-space-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--dc-space-lg);
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}

/* 代码块增强 */
.highlight {
  border-radius: var(--dc-radius-md);
  overflow: hidden;
}

/* 表格样式增强 */
.md-typeset table:not([class]) {
  border-radius: var(--dc-radius-md);
  overflow: hidden;
  box-shadow: var(--dc-shadow-sm);
}

/* 警告框样式增强 */
.md-typeset .admonition {
  border-radius: var(--dc-radius-md);
  box-shadow: var(--dc-shadow-sm);
}

/* 导航增强 */
.md-nav__item--active > .md-nav__link {
  color: var(--dc-primary);
  font-weight: 600;
}

/* 搜索结果增强 */
.md-search-result__item {
  border-radius: var(--dc-radius-md);
}

/* 标签样式 */
.md-tag {
  background: var(--dc-gradient-secondary);
  color: white;
  border-radius: var(--dc-radius-sm);
}
