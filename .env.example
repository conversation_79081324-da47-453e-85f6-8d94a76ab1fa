# DramaCraft 配置示例
# 将此文件复制为 .env 并填入您的实际配置值

# =============================================================================
# 大语言模型提供商配置
# =============================================================================

# 大模型提供商 (baidu, alibaba, tencent)
LLM__PROVIDER=baidu

# 百度千帆配置
LLM__API_KEY=你的百度API密钥
LLM__SECRET_KEY=你的百度密钥
LLM__MODEL_NAME=ERNIE-Bot-turbo

# 阿里通义千问配置 (如果使用阿里提供商)
# LLM__API_KEY=你的阿里API密钥
# LLM__MODEL_NAME=qwen-turbo

# 腾讯混元配置 (如果使用腾讯提供商)
# LLM__API_KEY=你的腾讯API密钥
# LLM__SECRET_KEY=你的腾讯密钥
# LLM__MODEL_NAME=hunyuan-lite

# 大模型生成参数
LLM__MAX_TOKENS=2000
LLM__TEMPERATURE=0.7
LLM__TIMEOUT=30

# =============================================================================
# 视频处理配置
# =============================================================================

# 输出和临时目录
VIDEO__OUTPUT_DIR=./output
VIDEO__TEMP_DIR=./temp

# 视频处理设置
VIDEO__MAX_VIDEO_DURATION=600
VIDEO__VIDEO_QUALITY=medium

# 字幕设置
VIDEO__SUBTITLE_FONT_SIZE=24
VIDEO__SUBTITLE_FONT_COLOR=white

# =============================================================================
# 剪映集成配置
# =============================================================================

# 剪映安装路径 (根据您的系统调整)
# Windows: C:\Program Files\JianYing\JianYing.exe
# macOS: /Applications/JianYing.app
JIANYING__INSTALLATION_PATH=/Applications/JianYing.app

# 剪映项目模板目录
JIANYING__PROJECT_TEMPLATE_DIR=./templates

# 自动化设置
JIANYING__AUTOMATION_DELAY=1.0
JIANYING__SCREENSHOT_ON_ERROR=true

# =============================================================================
# 服务配置
# =============================================================================

# 服务设置
SERVICE_NAME=dramacraft
SERVICE_VERSION=0.1.0
HOST=localhost
PORT=8000

# 性能设置
MAX_CONCURRENT_REQUESTS=10

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOGGING__LEVEL=INFO

# 日志文件路径 (留空则仅输出到控制台)
LOGGING__FILE_PATH=./logs/dramacraft.log

# 日志文件设置
LOGGING__MAX_FILE_SIZE=10485760  # 10MB
LOGGING__BACKUP_COUNT=5

# =============================================================================
# 开发设置
# =============================================================================

# 启用调试模式 (开发时设置为 true)
DEBUG=false
