name: 🔍 Code Quality Check

on:
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  quality:
    name: 🧹 Code Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
      
      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'
      
      - name: 📦 Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install ruff mypy
      
      - name: 🔍 Run Ruff linting
        run: |
          ruff check src/ --output-format=github || true
      
      - name: 🎨 Run Ruff formatting check
        run: |
          ruff format src/ --check || true
      
      - name: 🔬 Run MyPy type checking
        run: |
          mypy src/ --ignore-missing-imports || true
      
      - name: ✅ Quality check complete
        run: |
          echo "Code quality check completed"
          echo "Note: This workflow is informational and does not block merges"
