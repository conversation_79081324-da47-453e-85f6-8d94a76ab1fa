name: 📚 Deploy Documentation

on:
  push:
    branches: [ main ]
    paths:
      - 'docs/**'
      - 'mkdocs.yml'
      - '.github/workflows/docs.yml'
  workflow_dispatch:

# 设置权限以允许部署到GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# 确保只有一个部署任务同时运行
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # 构建文档
  build:
    name: 🔨 Build Documentation
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'
      
      - name: 📦 Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install mkdocs-material
      
      - name: 🔧 Setup Pages
        id: pages
        uses: actions/configure-pages@v4
      
      - name: 🏗️ Build documentation
        run: |
          mkdocs build --clean
        env:
          SITE_URL: ${{ steps.pages.outputs.base_url }}
      
      - name: 🧪 Test documentation
        run: |
          # 检查关键文件是否存在
          test -f site/index.html || (echo "❌ index.html not found" && exit 1)
          test -f site/getting-started/index.html || (echo "❌ getting-started/index.html not found" && exit 1)
          test -f site/api-reference/index.html || (echo "❌ api-reference/index.html not found" && exit 1)
          
          # 检查HTML内容
          grep -q "DramaCraft" site/index.html || (echo "❌ DramaCraft title not found in index.html" && exit 1)
          
          echo "✅ Documentation tests passed"
      
      - name: 📤 Upload Pages artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: site/

  # 部署到GitHub Pages
  deploy:
    name: 🚀 Deploy to GitHub Pages
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: 🌐 Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
      
      - name: 📊 Deployment summary
        run: |
          echo "🎉 Documentation deployed successfully!"
          echo "📍 URL: ${{ steps.deployment.outputs.page_url }}"
          echo "⏰ Deployed at: $(date)"
