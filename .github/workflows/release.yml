name: Release to PyPI

on:
  push:
    tags:
      - 'v*'
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., 0.1.0)'
        required: true
        type: string

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12"]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"
    
    - name: Set up Python ${{ matrix.python-version }}
      run: uv python install ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        uv sync --all-extras
    
    - name: Run linting
      run: |
        uv run ruff check src tests
        uv run black --check src tests
        uv run mypy src
    
    - name: Run tests
      run: |
        uv run pytest tests/ -v --cov=dramacraft --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"
    
    - name: Set up Python
      run: uv python install 3.11
    
    - name: Install build dependencies
      run: |
        uv add --dev build twine
    
    - name: Update version
      if: github.event.inputs.version
      run: |
        sed -i 's/version = ".*"/version = "${{ github.event.inputs.version }}"/' pyproject.toml
    
    - name: Build package
      run: |
        uv run python -m build
    
    - name: Check package
      run: |
        uv run twine check dist/*
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  publish-test:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/
    
    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"
    
    - name: Set up Python
      run: uv python install 3.11
    
    - name: Install twine
      run: uv add twine
    
    - name: Publish to Test PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.TEST_PYPI_API_TOKEN }}
      run: |
        uv run twine upload --repository testpypi dist/*

  publish:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'release' || startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/
    
    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"
    
    - name: Set up Python
      run: uv python install 3.11
    
    - name: Install twine
      run: uv add twine
    
    - name: Publish to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        uv run twine upload dist/*

  create-release:
    needs: publish
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Extract version from tag
      id: version
      run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
    
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: DramaCraft v${{ steps.version.outputs.VERSION }}
        body: |
          ## DramaCraft v${{ steps.version.outputs.VERSION }}
          
          ### 新功能
          - 详细的更新内容请查看 [CHANGELOG.md](https://github.com/dramacraft/dramacraft/blob/main/CHANGELOG.md)
          
          ### 安装
          ```bash
          pip install dramacraft==${{ steps.version.outputs.VERSION }}
          ```
          
          ### 使用uv安装
          ```bash
          uv add dramacraft==${{ steps.version.outputs.VERSION }}
          ```
          
          ### 快速开始
          ```bash
          # 启动MCP服务
          uv run dramacraft start
          ```
          
          完整文档: https://dramacraft.readthedocs.io
        draft: false
        prerelease: false

  docker:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'release' || startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: dramacraft/dramacraft
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
