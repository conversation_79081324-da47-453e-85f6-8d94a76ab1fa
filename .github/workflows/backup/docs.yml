name: Build and Deploy Documentation

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'docs/**'
      - 'src/**'
      - 'mkdocs.yml'
      - '.github/workflows/docs.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'docs/**'
      - 'src/**'
      - 'mkdocs.yml'
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

env:
  PYTHON_VERSION: "3.11"

jobs:
  # 构建文档
  build-docs:
    name: Build Documentation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install uv
        uses: astral-sh/setup-uv@v2

      - name: Install dependencies
        run: |
          uv sync --all-extras --dev
          uv pip install mkdocs-material mkdocs-mermaid2-plugin mkdocs-swagger-ui-tag

      - name: Setup Node.js for additional tools
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install Node.js dependencies
        run: |
          npm install -g @mermaid-js/mermaid-cli
          npm install -g swagger-ui-dist

      - name: Generate API documentation
        run: |
          uv run python scripts/generate_api_docs.py
          uv run python scripts/extract_examples.py

      - name: Build MkDocs site
        run: |
          uv run mkdocs build --clean --strict

      - name: Upload documentation artifacts
        uses: actions/upload-artifact@v4
        with:
          name: documentation-site
          path: site/
          retention-days: 30

      - name: Setup Pages
        if: github.ref == 'refs/heads/main'
        uses: actions/configure-pages@v3

      - name: Upload to GitHub Pages
        if: github.ref == 'refs/heads/main'
        uses: actions/upload-pages-artifact@v3
        with:
          path: site/

  # 部署到GitHub Pages
  deploy-pages:
    name: Deploy to GitHub Pages
    if: github.ref == 'refs/heads/main'
    needs: build-docs
    runs-on: ubuntu-latest
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

  # 检查文档质量
  docs-quality:
    name: Documentation Quality Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install uv
        uses: astral-sh/setup-uv@v2

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyyaml

      - name: Validate documentation structure
        run: |
          python scripts/validate_docs.py

  # 生成文档统计
  docs-stats:
    name: Documentation Statistics
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip

      - name: Generate documentation statistics
        run: |
          python scripts/docs_stats.py > docs_stats.json

      - name: Upload statistics
        uses: actions/upload-artifact@v4
        with:
          name: docs-statistics
          path: docs_stats.json

      - name: Comment PR with stats
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const stats = JSON.parse(fs.readFileSync('docs_stats.json', 'utf8'));
            
            const comment = `## 📊 文档统计报告
            
            | 指标 | 数值 |
            |------|------|
            | 总页面数 | ${stats.total_pages} |
            | 总字数 | ${stats.total_words} |
            | API端点数 | ${stats.api_endpoints} |
            | 代码示例数 | ${stats.code_examples} |
            | 图片数量 | ${stats.images} |
            | 内部链接数 | ${stats.internal_links} |
            | 外部链接数 | ${stats.external_links} |
            | 文档覆盖率 | ${stats.coverage}% |
            
            📈 文档质量评分: **${stats.quality_score}/100**
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # 多语言文档构建
  build-i18n:
    name: Build Internationalization
    runs-on: ubuntu-latest
    strategy:
      matrix:
        language: [en, zh]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install uv
        uses: astral-sh/setup-uv@v2

      - name: Install dependencies
        run: |
          uv sync --dev
          uv pip install mkdocs-material mkdocs-static-i18n

      - name: Build documentation for ${{ matrix.language }}
        run: |
          export MKDOCS_LANG=${{ matrix.language }}
          uv run mkdocs build -f mkdocs-${{ matrix.language }}.yml

      - name: Upload ${{ matrix.language }} documentation
        uses: actions/upload-artifact@v3
        with:
          name: docs-${{ matrix.language }}
          path: site-${{ matrix.language }}/
