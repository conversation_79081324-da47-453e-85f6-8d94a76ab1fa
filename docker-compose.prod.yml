# DramaCraft 生产环境 Docker Compose 配置
# 企业级部署配置，包含完整的微服务架构和监控

version: '3.8'

# 网络配置
networks:
  dramacraft-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  monitoring-network:
    driver: bridge

# 卷配置
volumes:
  dramacraft-data:
    driver: local
  dramacraft-logs:
    driver: local
  dramacraft-cache:
    driver: local
  redis-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  nginx-logs:
    driver: local

services:
  # DramaCraft 主服务
  dramacraft:
    image: dramacraft/dramacraft:latest
    container_name: dramacraft-server
    restart: unless-stopped
    environment:
      # 基础配置
      DRAMACRAFT_CONFIG: /app/config/config.yaml
      DRAMACRAFT_HOST: 0.0.0.0
      DRAMACRAFT_PORT: 8080
      DRAMACRAFT_WORKERS: 4
      DRAMACRAFT_LOG_LEVEL: INFO
      
      # 数据库配置
      DATABASE_URL: postgresql://dramacraft:${POSTGRES_PASSWORD}@postgres:5432/dramacraft
      
      # Redis配置
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      CACHE_TYPE: redis
      
      # AI服务配置
      BAIDU_API_KEY: ${BAIDU_API_KEY}
      BAIDU_SECRET_KEY: ${BAIDU_SECRET_KEY}
      ALIBABA_API_KEY: ${ALIBABA_API_KEY}
      TENCENT_SECRET_ID: ${TENCENT_SECRET_ID}
      TENCENT_SECRET_KEY: ${TENCENT_SECRET_KEY}
      
      # 安全配置
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      SESSION_ENCRYPTION_KEY: ${SESSION_ENCRYPTION_KEY}
      
      # 性能配置
      MAX_CONCURRENT_JOBS: 8
      MEMORY_LIMIT: 8GB
      GPU_ACCELERATION: ${GPU_ACCELERATION:-false}
      
      # 监控配置
      PROMETHEUS_ENABLED: true
      JAEGER_ENABLED: true
    volumes:
      - dramacraft-data:/app/data
      - dramacraft-logs:/app/logs
      - dramacraft-cache:/app/cache
      - ./docker/config.yaml:/app/config/config.yaml:ro
    networks:
      - dramacraft-network
      - monitoring-network
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
      replicas: 2
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # DramaCraft 工作进程
  dramacraft-worker:
    image: dramacraft/dramacraft:latest
    container_name: dramacraft-worker
    restart: unless-stopped
    command: ["worker"]
    environment:
      DRAMACRAFT_CONFIG: /app/config/config.yaml
      DATABASE_URL: postgresql://dramacraft:${POSTGRES_PASSWORD}@postgres:5432/dramacraft
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/1
      QUEUE_TYPE: redis
      QUEUE_WORKERS: 6
      MAX_CONCURRENT_JOBS: 12
      MEMORY_LIMIT: 16GB
    volumes:
      - dramacraft-data:/app/data
      - dramacraft-logs:/app/logs
      - dramacraft-cache:/app/cache
      - ./docker/config.yaml:/app/config/config.yaml:ro
    networks:
      - dramacraft-network
    depends_on:
      - postgres
      - redis
      - dramacraft
    deploy:
      resources:
        limits:
          cpus: '8.0'
          memory: 16G
        reservations:
          cpus: '4.0'
          memory: 8G
      replicas: 3

  # PostgreSQL 数据库（主从复制）
  postgres:
    image: postgres:15-alpine
    container_name: dramacraft-postgres-master
    restart: unless-stopped
    environment:
      POSTGRES_DB: dramacraft
      POSTGRES_USER: dramacraft
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${POSTGRES_REPLICATION_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./docker/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - dramacraft-network
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dramacraft -d dramacraft"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Redis 集群
  redis:
    image: redis:7-alpine
    container_name: dramacraft-redis-master
    restart: unless-stopped
    command: >
      redis-server 
      --appendonly yes 
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 2gb 
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis-data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - dramacraft-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Nginx 负载均衡器
  nginx:
    image: nginx:alpine
    container_name: dramacraft-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
      - ./docs/build:/usr/share/nginx/html/docs:ro
    networks:
      - dramacraft-network
    depends_on:
      - dramacraft
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: dramacraft-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./docker/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - monitoring-network
      - dramacraft-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: dramacraft-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_SECURITY_SECRET_KEY: ${GRAFANA_SECRET_KEY}
      GF_DATABASE_TYPE: postgres
      GF_DATABASE_HOST: postgres:5432
      GF_DATABASE_NAME: grafana
      GF_DATABASE_USER: grafana
      GF_DATABASE_PASSWORD: ${GRAFANA_DB_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - monitoring-network
      - dramacraft-network
    depends_on:
      - prometheus
      - postgres
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Jaeger 分布式追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: dramacraft-jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "14268:14268"
      - "6831:6831/udp"
      - "6832:6832/udp"
    environment:
      COLLECTOR_OTLP_ENABLED: true
      SPAN_STORAGE_TYPE: elasticsearch
      ES_SERVER_URLS: http://elasticsearch:9200
    networks:
      - monitoring-network
      - dramacraft-network
    depends_on:
      - elasticsearch
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Elasticsearch 日志和追踪存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: dramacraft-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
      - cluster.name=dramacraft-cluster
      - node.name=dramacraft-node-1
    volumes:
      - ./docker/elasticsearch/data:/usr/share/elasticsearch/data
      - ./docker/elasticsearch/config:/usr/share/elasticsearch/config:ro
    ports:
      - "9200:9200"
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Kibana 日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: dramacraft-kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
      SERVER_NAME: dramacraft-kibana
      SERVER_HOST: 0.0.0.0
    ports:
      - "5601:5601"
    networks:
      - monitoring-network
    depends_on:
      - elasticsearch
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # AlertManager 告警管理
  alertmanager:
    image: prom/alertmanager:latest
    container_name: dramacraft-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./docker/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
