"""
DramaCraft 安全模块

提供企业级安全功能：
- OAuth 2.0/JWT 认证
- 基于角色的访问控制 (RBAC)
- 多因素认证 (MFA)
- 数据加密和安全审计
"""

from .audit import AuditLogger, SecurityAudit
from .auth import AuthManager, JWTManager, OAuthProvider
from .encryption import DataEncryption, SecureStorage
from .mfa import MFAManager, TOTPProvider
from .rbac import PermissionManager, RoleManager

__all__ = [
    "AuthManager",
    "JWTManager",
    "OAuthProvider",
    "DataEncryption",
    "SecureStorage",
    "RoleManager",
    "PermissionManager",
    "SecurityAudit",
    "AuditLogger",
    "MFAManager",
    "TOTPProvider",
]
