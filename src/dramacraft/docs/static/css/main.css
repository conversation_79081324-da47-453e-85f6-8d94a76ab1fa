/* DramaCraft 文档系统主样式文件 */

/* CSS变量定义 */
:root {
  /* 颜色系统 */
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --secondary-color: #f59e0b;
  --secondary-light: #fbbf24;
  --secondary-dark: #d97706;
  
  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 语义色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
  
  /* 字体 */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', '<PERSON>ra Code', Consolas, monospace;
  
  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* 间距 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* 圆角 */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 过渡 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* 布局 */
  --header-height: 4rem;
  --sidebar-width: 16rem;
  --toc-width: 14rem;
  --container-max-width: 1200px;
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 容器 */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* 头部 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
  color: var(--gray-900);
}

.logo-image {
  width: 2rem;
  height: 2rem;
}

.logo-text {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.version-badge {
  padding: var(--space-1) var(--space-2);
  background: var(--secondary-color);
  color: white;
  font-size: var(--text-xs);
  font-weight: 600;
  border-radius: var(--radius-md);
}

/* 导航 */
.nav {
  display: flex;
  gap: var(--space-6);
}

.nav-link {
  color: var(--gray-600);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* 头部操作区 */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

/* 搜索 */
.search-container {
  position: relative;
}

.search-input {
  width: 16rem;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  background: white;
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 20rem;
  overflow-y: auto;
  z-index: 1001;
  display: none;
}

/* 语言选择器 */
.language-selector {
  position: relative;
}

.language-btn {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background: white;
  color: var(--gray-700);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.language-btn:hover {
  border-color: var(--gray-400);
}

.language-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 8rem;
  z-index: 1001;
  display: none;
}

.language-option {
  display: block;
  padding: var(--space-2) var(--space-3);
  color: var(--gray-700);
  text-decoration: none;
  font-size: var(--text-sm);
  transition: background-color var(--transition-fast);
}

.language-option:hover {
  background: var(--gray-50);
}

/* 主题切换 */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background: white;
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.theme-toggle:hover {
  border-color: var(--gray-400);
  color: var(--gray-800);
}

.theme-icon-dark {
  display: none;
}

/* GitHub链接 */
.github-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.github-link:hover {
  color: var(--gray-800);
  background: var(--gray-100);
}

/* 主要布局 */
.main-container {
  display: grid;
  grid-template-columns: var(--sidebar-width) 1fr var(--toc-width);
  margin-top: var(--header-height);
  min-height: calc(100vh - var(--header-height));
}

/* 侧边栏 */
.sidebar {
  background: white;
  border-right: 1px solid var(--gray-200);
  overflow-y: auto;
  position: sticky;
  top: var(--header-height);
  height: calc(100vh - var(--header-height));
}

.sidebar-content {
  padding: var(--space-6) var(--space-4);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.sidebar-header h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--gray-900);
}

.sidebar-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--gray-600);
  cursor: pointer;
}

/* 侧边栏导航 */
.sidebar-nav {
  space-y: var(--space-2);
}

.nav-section {
  margin-bottom: var(--space-4);
}

.nav-item {
  display: block;
  padding: var(--space-2) var(--space-3);
  color: var(--gray-600);
  text-decoration: none;
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  margin-bottom: var(--space-1);
}

.nav-item:hover {
  background: var(--gray-100);
  color: var(--gray-900);
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
}

.nav-item.level-2 {
  font-weight: 600;
}

.nav-item.level-3 {
  padding-left: var(--space-6);
}

.nav-item.level-4 {
  padding-left: var(--space-8);
  font-size: var(--text-xs);
}

.nav-children {
  margin-left: var(--space-3);
  border-left: 1px solid var(--gray-200);
  padding-left: var(--space-3);
}

/* 主内容区 */
.content {
  background: white;
  overflow-y: auto;
}

.content-wrapper {
  max-width: 48rem;
  margin: 0 auto;
  padding: var(--space-8) var(--space-6);
}

/* 面包屑 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-8);
  font-size: var(--text-sm);
}

.breadcrumb-item {
  color: var(--gray-600);
  text-decoration: none;
}

.breadcrumb-item:hover {
  color: var(--primary-color);
}

.breadcrumb-item.current {
  color: var(--gray-900);
  font-weight: 500;
}

.breadcrumb-separator {
  color: var(--gray-400);
}

/* 文档内容 */
.documentation {
  line-height: 1.7;
}

.doc-title {
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

.doc-subtitle {
  font-size: var(--text-xl);
  color: var(--gray-600);
  margin-bottom: var(--space-12);
}

/* 概述卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(16rem, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-16);
}

.overview-card {
  padding: var(--space-6);
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-icon {
  font-size: var(--text-3xl);
  margin-bottom: var(--space-4);
}

.overview-card h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.overview-card p {
  color: var(--gray-600);
  margin-bottom: var(--space-4);
}

.card-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: var(--text-sm);
}

.card-link:hover {
  color: var(--primary-dark);
}

/* 文档章节 */
.doc-section {
  margin-bottom: var(--space-16);
}

.section-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--gray-200);
}

.section-content {
  margin-bottom: var(--space-8);
}

/* 右侧目录 */
.toc {
  background: var(--gray-50);
  border-left: 1px solid var(--gray-200);
  overflow-y: auto;
  position: sticky;
  top: var(--header-height);
  height: calc(100vh - var(--header-height));
}

.toc-content {
  padding: var(--space-6) var(--space-4);
}

.toc-title {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

.toc-nav {
  space-y: var(--space-1);
}

.toc-nav a {
  display: block;
  padding: var(--space-1) var(--space-2);
  color: var(--gray-600);
  text-decoration: none;
  font-size: var(--text-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.toc-nav a:hover {
  background: var(--gray-200);
  color: var(--gray-900);
}

.toc-nav a.active {
  background: var(--primary-color);
  color: white;
}

/* 页脚 */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  margin-top: var(--space-20);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(12rem, 1fr));
  gap: var(--space-8);
  padding: var(--space-16) 0 var(--space-8);
}

.footer-section h4 {
  color: white;
  font-weight: 600;
  margin-bottom: var(--space-4);
}

.footer-section ul {
  list-style: none;
  space-y: var(--space-2);
}

.footer-section a {
  color: var(--gray-400);
  text-decoration: none;
  font-size: var(--text-sm);
  transition: color var(--transition-fast);
}

.footer-section a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid var(--gray-700);
  padding: var(--space-6) 0;
  text-align: center;
  font-size: var(--text-sm);
  color: var(--gray-500);
}
