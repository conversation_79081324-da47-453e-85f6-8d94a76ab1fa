# DramaCraft 简化文档配置
# 专注于稳定的文档构建和部署

site_name: DramaCraft
site_description: 企业级视频编辑MCP服务 - 专业的AI驱动视频处理解决方案
site_author: agions
site_url: https://agions.github.io/dramacraft

# 仓库信息
repo_name: Agions/dramacraft
repo_url: https://github.com/Agions/dramacraft
edit_uri: edit/main/docs/

# 版权信息
copyright: |
  &copy; 2025 agions. 保留所有权利. <br>
  <a href="mailto:<EMAIL>">联系我们</a>

# 主题配置
theme:
  name: material
  language: zh
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - navigation.top
    - search.highlight
    - search.share
    - content.code.copy
    - content.tabs.link

  palette:
    # 浅色模式
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: blue
      accent: orange
      toggle:
        icon: material/brightness-7
        name: 切换到深色模式
    
    # 深色模式
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: blue
      accent: orange
      toggle:
        icon: material/brightness-4
        name: 切换到浅色模式

  font:
    text: Roboto
    code: Roboto Mono

  icon:
    logo: material/video
    repo: fontawesome/brands/github

# 导航结构
nav:
  - 首页: index.md
  - 快速开始: getting-started/index.md
  - API参考: api-reference/index.md

# 插件配置
plugins:
  - search:
      lang: 
        - zh
        - en

# Markdown扩展
markdown_extensions:
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
      title: 目录
  - pymdownx.details
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.superfences
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true

# 额外配置
extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/Agions/dramacraft
      name: GitHub仓库
    - icon: fontawesome/solid/envelope
      link: mailto:<EMAIL>
      name: 邮件联系

# 额外CSS
extra_css:
  - assets/stylesheets/extra.css

# 严格模式关闭（避免构建失败）
strict: false

# 使用目录URLs
use_directory_urls: true
