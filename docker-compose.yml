# DramaCraft Docker Compose 配置
# 用于开发和测试环境的容器编排

version: '3.8'

services:
  # DramaCraft MCP 服务
  dramacraft:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: dramacraft-mcp
    restart: unless-stopped
    environment:
      # 环境配置
      - PYTHONPATH=/home/<USER>/src
      - LLM__PROVIDER=${LLM__PROVIDER:-baidu}
      - LLM__API_KEY=${LLM__API_KEY}
      - LLM__SECRET_KEY=${LLM__SECRET_KEY}
      - VIDEO__TEMP_DIR=/home/<USER>/temp
      - JIANYING__INSTALLATION_PATH=${JIANYING__INSTALLATION_PATH}
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FORMAT=${LOG_FORMAT:-json}
    volumes:
      # 代码挂载（开发模式）
      - ./src:/home/<USER>/src:ro
      - ./tests:/home/<USER>/tests:ro
      - ./configs:/home/<USER>/configs:ro
      # 数据目录
      - ./output:/home/<USER>/output
      - ./logs:/home/<USER>/logs
      - ./temp:/home/<USER>/temp
      - ./assets:/home/<USER>/assets
      # 配置文件
      - ./.env:/home/<USER>/.env:ro
    ports:
      - "8000:8000"  # HTTP API端口（如果需要）
    networks:
      - dramacraft-network
    healthcheck:
      test: ["CMD", "uv", "run", "python", "-c", "import dramacraft; print('OK')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - postgres

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: dramacraft-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-dramacraft123}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-dramacraft123}
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - dramacraft-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL 数据库（用于存储项目元数据）
  postgres:
    image: postgres:15-alpine
    container_name: dramacraft-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-dramacraft}
      - POSTGRES_USER=${POSTGRES_USER:-dramacraft}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-dramacraft123}
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - dramacraft-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-dramacraft} -d ${POSTGRES_DB:-dramacraft}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理（生产环境）
  nginx:
    image: nginx:alpine
    container_name: dramacraft-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./docs/website:/usr/share/nginx/html:ro
    networks:
      - dramacraft-network
    depends_on:
      - dramacraft
    profiles:
      - production

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: dramacraft-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - dramacraft-network
    profiles:
      - monitoring

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: dramacraft-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    networks:
      - dramacraft-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

  # 日志聚合 - ELK Stack (可选)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: dramacraft-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - dramacraft-network
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: dramacraft-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - dramacraft-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

# 网络配置
networks:
  dramacraft-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  redis-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  elasticsearch-data:
    driver: local

# 使用示例:
# 
# 开发环境启动:
# docker-compose up -d
# 
# 生产环境启动:
# docker-compose --profile production up -d
# 
# 包含监控的启动:
# docker-compose --profile monitoring up -d
# 
# 完整环境启动:
# docker-compose --profile production --profile monitoring --profile logging up -d
# 
# 查看日志:
# docker-compose logs -f dramacraft
# 
# 停止服务:
# docker-compose down
# 
# 清理数据:
# docker-compose down -v
